#include <vector>
#include <iomanip> //cout输出16进制hex
#include "ImGuiELGS.h"
#include "touch.h"
#include "ImGuidraft.h"

vector<float> sl1;
vector<uintptr_t> dz; // 第一层偏移
vector<uintptr_t> tz; // 跳转记录
vector<float> sl2;
vector<uintptr_t> dz2; // 第二层跳转的偏移
string ItemName;
touch touchdriven;
const int max_obj_number = 500;
int scid, 自身武器;
char *CasName;
struct SHARED_INFORMATION Shared_Information[max_obj_number];
struct VEHICLE_INFORMATION Vehicle_Information[max_obj_number];
struct THROW_INFORMATION Throw_Information[max_obj_number];
struct SUNDRIES_INFORMATION Sundries_Information[max_obj_number];
struct RESOURCE_INFORMATION Resource_Information[max_obj_number];
bool IsGameStart;
int aimitar = -1;
int aiminur = -1;
float aimi_abs = 0;
float aimi_x_abs = 0;
float aimi_y_abs = 0;
bool aimibot = false;
bool aimifal = false;
bool aimidage = false;
string aiminame = "";
struct Timer aimitiem;
float aimidistance = 0;
bool aimiswitch = false;
unsigned long looptime = 0;
struct VecTor3 aimista = {0, 0, 0};
struct VecTor3 aimiend = {0, 0, 0};
struct VecTor3 aimiant = {0, 0, 0};

int IsType = 0;

int OBJECTCOUNT[8] = {0};

float LeftFoot_Skeleton = 0;
float RihtFoot_Skeleton = 0;
ItemTypeIdentifier identifier;
int SelfHandheldWeaponValue = 0;
struct VecTor3 HeadSkeleton = {0, 0, 0};
struct VecTor3 SelfCoordinate = {0, 0, 0};
struct VecTor3 LeftFootSkeleton = {0, 0, 0};
struct VecTor3 RihtFootSkeleton = {0, 0, 0};
struct VecTor3 ObjectCoordinate = {0, 0, 0};
struct VecTor3 SelfViewCoordinate = {0, 0, 0};
void ImGuiELGS::ImGuiWindowDraw()
{

	if (initializetouchorread)
	{
		InitializeColor();
		// 使用新的内核触摸驱动初始化
		touchdriven.SetScreenResolution(resolution_information.ScreenWidth, resolution_information.ScreenHeiht);
		touchdriven.Touch_Init();
		initializetouchorread = false;
	}
	static int SkeletonList_New[][2]{{5, 4}, {4, 0}, {4, 11}, {11, 12}, {12, 13}, {4, 33}, {33, 34}, {34, 35}, {0, 55}, {55, 56}, {56, 57}, {0, 59}, {59, 60}, {60, 61}};
	static int SkeletonList_Old[][2]{{5, 4}, {4, 0}, {4, 11}, {11, 12}, {12, 63}, {4, 32}, {32, 33}, {33, 62}, {0, 52}, {52, 53}, {53, 54}, {0, 56}, {56, 57}, {57, 58}};
	ImDrawList *drawlist = ImGui::GetForegroundDrawList();
	if (initializedraw && Pid > 0)
	{
		IsGameStart = GetPointer(ModulesBase[0], 0x12161358, 0x98, 0);
		if (IsGameStart)
		{
			if (imguiswitch_information.boolswitch[3])
			{

				drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, imguiswitch_information.floatswitch[43], ImColor(10, 10, 10, 80));

				drawlist->AddCircle({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, imguiswitch_information.floatswitch[43], ImColor(255, 255, 255, 255), 0, 2);
				drawlist->AddCircle({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, imguiswitch_information.floatswitch[43] / 2, ImColor(255, 255, 255, 255), 0, 2);
			}

			uintptr_t Controller = GetPointer(ModulesBase[0], 0x12161358, 0x98, 0x88, 0x30, 0); // 玩家控制器
			uintptr_t CameraAddress = GetPointer(Controller, 0x608, 0);							// 相机数量从玩家控制器上偏移

			uintptr_t MatrixAddress = GetPointer(ModulesBase[0], 0x12132D60, 0x20, 0x270);
			WorldAddress = GetPointer(ModulesBase[0], 0x12161358, 0x90, 0);
			ArrayAddress = GetPointer(WorldAddress, 0xa0, 0);
			ArraysCount = read<int>(WorldAddress + 0xa8);
			SelfAddress = GetPointer(ModulesBase[0], 0x12161358, 0x98, 0x88, 0x30, 0x32a8, 0);
			自身动作 = read<int>(GetPointer(ModulesBase[0], 0x12161358, 0x98, 0x88, 0x30, 0x32a8, 0x1538, 0)); // 就是动作 填动作数据
			if (WorldAddress != 0 || Controller != 0 || ArrayAddress != 0)
			{

				IsCamera = read<int>(SelfAddress + 0x1668);
				IsFire = read<int>(SelfAddress + 0x23e0);
				OwnTeam = read<int>(Controller + 0xb18);
				RootPoint = read<uintptr_t>(SelfAddress + 0x278); // 坐标x
				if (read<int>(RootPoint) != 0)
				{
					read(RootPoint + 0x200, &SelfCoordinate, sizeof(SelfCoordinate)); // 坐标y
				}
				read(CameraAddress + 0x600, &SelfViewCoordinate, sizeof(SelfViewCoordinate));
				read(Controller + 0x5a8, &touch_information.MouseCoordinate, sizeof(touch_information.MouseCoordinate));
				SelfHandheldWeaponValue = read<int>(read<uintptr_t>(SelfAddress + 0xfe8) + 0xbd0);
			}

			OBJECTCOUNT[0] = 0;
			OBJECTCOUNT[1] = 0;
			OBJECTCOUNT[2] = 0;
			OBJECTCOUNT[3] = 0;
			OBJECTCOUNT[4] = 0;
			OBJECTCOUNT[5] = 0; // 物资箱结构体
			OBJECTCOUNT[6] = 0;
			OBJECTCOUNT[7] = 0;
			OBJECTCOUNT[8] = 0; // 类
			int 人机数量 = 0;
			int 真人数量 = 0;

			if (read(MatrixAddress, &Matrix[0][0], 64))
			{
				touch_information.Scal = sqrt(Matrix[0][0] * Matrix[0][0] + Matrix[1][0] * Matrix[1][0] + Matrix[2][0] * Matrix[2][0]);
			}

			for (int count = 0; count < ArraysCount; count++)
			{

				uintptr_t ObjectAddress = read<uintptr_t>(ArrayAddress + 0x8 * count);

				if (ObjectAddress == 0 || ArrayAddress == ObjectAddress)
				{
					continue;
				}

				uintptr_t RootPoint = read<uintptr_t>(ObjectAddress + 0x278);
				if (read<int>(RootPoint) != 0)
				{
					read(RootPoint + 0x200, &ObjectCoordinate, sizeof(ObjectCoordinate));
				}

				if (ObjectCoordinate.x == 0 || ObjectCoordinate.y == 0 || ObjectCoordinate.z == 0)
				{

					continue;
				}

				if (read(MatrixAddress, &Matrix[0][0], 64))
				{
					touch_information.Scal = sqrt(Matrix[0][0] * Matrix[0][0] + Matrix[1][0] * Matrix[1][0] + Matrix[2][0] * Matrix[2][0]);
				}

				if (read<float>(ObjectAddress + 0x3518) == 479.5)
				{

					GetDistance(ObjectCoordinate, SelfCoordinate, Shared_Information[OBJECTCOUNT[0]].Distance);

					Shared_Information[OBJECTCOUNT[0]].Team = read<int>(ObjectAddress + 0xac0); // 对象队伍

					if (Shared_Information[OBJECTCOUNT[0]].Team == OwnTeam)
					{
						continue;
					} // 队伍等于自己队伍或队伍小于1不绘制

					Shared_Information[OBJECTCOUNT[0]].Health = read<float>(ObjectAddress + 0xed8) * 100 / read<float>(ObjectAddress + 0xee0); // 当前血量
					if (Shared_Information[OBJECTCOUNT[0]].Health > 100)
					{
						continue;
					} // 血量大于100不绘制

					// 骨骼一列的东西
					MeshAddress = read<uintptr_t>(ObjectAddress + 0x600);
					if (read<int>(MeshAddress) != 0)
					{
						ReadBone(MeshAddress + 0x1F0, MeshTrans);
						TransformTurnMatrix(XMatrix, MeshTrans);
					}
					BoneAddress = read<uintptr_t>(MeshAddress + 0x7F8) + 0x30;
					Shared_Information[OBJECTCOUNT[0]].IsBoot = read<int>(ObjectAddress + 0xadc); // 人机判断
					if (aigl && Shared_Information[OBJECTCOUNT[0]].IsBoot == 1)
					{
						continue;
					}
					if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 1)
					{
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[6]; // 人机颜色
						sprintf(Shared_Information[OBJECTCOUNT[0]].PlayerName, "人机%d", OBJECTCOUNT[0]);
					}
					else if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 0)
					{
						Shared_Information[OBJECTCOUNT[0]].ColorChanGeable = imguiswitch_information.colorswitch[4];	// 真人颜色
						GetUTF8(Shared_Information[OBJECTCOUNT[0]].PlayerName, read<uintptr_t>(ObjectAddress + 0xa40)); // 获取真人名字
					}
					GetBoneTransform(5, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[0]);
					GetBoneTransform(4, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[1]);
					if (read<int>(MeshAddress + 0x7F8 + 0x8) == 68)
					{
						GetBoneTransform(57, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
						GetBoneTransform(61, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
					}
					else
					{
						GetBoneTransform(54, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[2]);
						GetBoneTransform(58, Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[3], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[3]);
					}

					LeftFootSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[1];
					LeftFootSkeleton.z -= 20;
					WorldTurnScreen(LeftFoot_Skeleton, LeftFootSkeleton);

					RihtFootSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[2];
					RihtFootSkeleton.z -= 20;
					WorldTurnScreen(RihtFoot_Skeleton, RihtFootSkeleton);

					HeadSkeleton = Shared_Information[OBJECTCOUNT[0]].WorldSkeletonCoordinates[0];
					HeadSkeleton.z += 20;
					WorldTurnScreen(Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates, HeadSkeleton);

					Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.x = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.z / 2;
					Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.y = Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates;
					Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.x = Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x + Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.z / 2;
					Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.y = max(LeftFoot_Skeleton, RihtFoot_Skeleton);

					GetRadarCoordinates(Shared_Information[OBJECTCOUNT[0]].RadarCoordinates, touch_information.MouseCoordinate.y, ObjectCoordinate, SelfCoordinate, imguiswitch_information.floatswitch[42]);

					if (WorldTurnScreen(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates, Shared_Information[OBJECTCOUNT[0]].ScreenCamera, ObjectCoordinate))
					{
						if (!imguiswitch_information.boolswitch[5])
						{
							if (imguiswitch_information.boolswitch[0] && Shared_Information[OBJECTCOUNT[0]].Team != -1)
							{ // 骨骼绘制

								for (int i = 0; i < 14; i++)
								{

									if (read<int>(MeshAddress + 0x7F8 + 0x8) == 68)
									{
										GetBoneTransform(SkeletonList_New[i][0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i]);
										GetBoneTransform(SkeletonList_New[i][1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i]);
									}
									else
									{
										GetBoneTransform(SkeletonList_Old[i][0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i]);
										GetBoneTransform(SkeletonList_Old[i][1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i]);
									}
									skeleton(drawlist, {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].y}, {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].y}, imguiswitch_information.colorswitch[8], imguiswitch_information.floatswitch[57]);
								}
							}

							if (imguiswitch_information.boolswitch[1])
							{ // 方框绘制

								DrawPlayerBox(ImGui::GetForegroundDrawList(), Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.y, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.y, imguiswitch_information.colorswitch[7], imguiswitch_information.floatswitch[57]);
								DrawPlayerBox(ImGui::GetForegroundDrawList(), Shared_Information[aimitar].BottomLeftFootSkeleton.x, Shared_Information[aimitar].BottomRihtFootSkeleton.x, Shared_Information[aimitar].BottomRihtFootSkeleton.y, Shared_Information[aimitar].HeadSkeletonCoordinates, Shared_Information[aimitar].ScreenCoordinates.x, Shared_Information[aimitar].ScreenCoordinates.y, ImColor(255, 0, 0, 255), imguiswitch_information.floatswitch[57]);
								// drawlist->AddRect({Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomLeftFootSkeleton.y}, {Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.x, Shared_Information[OBJECTCOUNT[0]].BottomRihtFootSkeleton.y}, imguiswitch_information.colorswitch[7],imguiswitch_information.floatswitch[57]);
							}
						}
						else if (imguiswitch_information.boolswitch[5])
						{
							if (imguiswitch_information.boolswitch[0] && Shared_Information[OBJECTCOUNT[0]].Distance <= imguiswitch_information.intswitch[91])
							{
								for (int i = 0; i < 14; i++)
								{
									if (read<int>(MeshAddress + 0x7F8 + 0x8) == 68)
									{
										GetBoneTransform(SkeletonList_New[i][0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i]);
										GetBoneTransform(SkeletonList_New[i][1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i]);
									}
									else
									{
										GetBoneTransform(SkeletonList_Old[i][0], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i]);
										GetBoneTransform(SkeletonList_Old[i][1], Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i]);
									}
									drawlist->AddLine({Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonSta[i].y}, {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonEnd[i].y}, Shared_Information[OBJECTCOUNT[0]].ColorChanGeable, imguiswitch_information.floatswitch[57]);
								}
							}
						}
						if (imguiswitch_information.boolswitch[2])
						{ // 射线绘制
							DrawLines(drawlist, resolution_information, Shared_Information, OBJECTCOUNT[0], imguiswitch_information);
						}

						if (imguiswitch_information.boolswitch[6])
						{ // 血条绘制
							HealthBarType healthBarType;
							switch (血条)
							{
							case 0:
								healthBarType = CircleArc;
								break;
							case 1:
								healthBarType = RectangleFilled;
								break;
							case 2:
								healthBarType = CustomRectangle;
							}
							DrawHealthBar(drawlist, Shared_Information, healthBarType, imguiswitch_information, OBJECTCOUNT[0]);
						}
						if (imguiswitch_information.boolswitch[7])
						{ // 距离绘制
							string Text = "";
							Text += to_string((int)Shared_Information[OBJECTCOUNT[0]].Distance) += "米";
							ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 18);
							drawlist->AddText(NULL, 18, {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (TextSize.x / 2), Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.y + Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.z}, imguiswitch_information.colorswitch[0], Text.c_str());
						}

						if (imguiswitch_information.boolswitch[8])
						{ // 名字绘制

							string Text = "";
							if (Shared_Information[OBJECTCOUNT[0]].Team == 996)
							{
								Text += "人机";
							}
							else
							{
								Text += to_string((int)Shared_Information[OBJECTCOUNT[0]].Team);
								Text += "·";
								Text += Shared_Information[OBJECTCOUNT[0]].PlayerName;
							}
							ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 18);
							drawlist->AddText(NULL, 18, {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (TextSize.x / 2), Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 20 - (TextSize.y / 2)}, imguiswitch_information.colorswitch[1], Text.c_str());
						}
						char *名称;
						ImTextureID 图片;

						if (imguiswitch_information.boolswitch[9] && 获取枪械信息1(scid, &图片, &名称))
						{ // 手持绘制

							string Text = "";
						//	Text += 获取人物动作(人物状态);
							Text += " | ";
							Text += GetHol(scid);
							ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 18);
							drawlist->AddText(NULL, 18, {Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - (TextSize.x / 2), Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 40 - (TextSize.y / 2)}, imguiswitch_information.colorswitch[2], Text.c_str());
							drawlist->AddImage(图片, ImVec2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x - 50, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 105), ImVec2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinates.x + 60, Shared_Information[OBJECTCOUNT[0]].HeadSkeletonCoordinates - 70));
						}

						if (initializeaimi && imguiswitch_information.boolswitch[52] && aiminur == OBJECTCOUNT[0])
						{ // 自瞄线
							drawlist->AddCircle(
								{Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].x,
								 Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].y},
								20,							 // 圆的半径
								ImColor(255, 255, 255, 255), // 白色
								12,							 // 圆边的顶点数，增加这个值可以让圆看起来更平滑
								2.0f						 // 圆边线的宽度
							);
							drawlist->AddLine({resolution_information.Width, resolution_information.Heiht}, {Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].x, Shared_Information[OBJECTCOUNT[0]].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].y}, ImColor(255, 255, 255, 255), 2);
						}
					}

					if (imguiswitch_information.boolswitch[4])
					{ // 敌背绘制
						WorldTurnScreen(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou, ObjectCoordinate, false);
						if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 0)
						{
							OffScreen(VecTor2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.x, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.y), Shared_Information[OBJECTCOUNT[0]].ScreenCamera, ImColor(arr[Shared_Information[OBJECTCOUNT[0]].Team % length]), imguiswitch_information.floatswitch[50] + dx + Shared_Information[OBJECTCOUNT[0]].Distance * 0.3);
						}
						else
						{
							OffScreen(VecTor2(Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.x, Shared_Information[OBJECTCOUNT[0]].ScreenCoordinateshou.y), Shared_Information[OBJECTCOUNT[0]].ScreenCamera, ImColor(255, 255, 255, 255), imguiswitch_information.floatswitch[50] + dx + Shared_Information[OBJECTCOUNT[0]].Distance * 0.3);
						}
					}

					if (imguiswitch_information.boolswitch[3])
					{ // 雷达绘制
						if (!透明)
						{
							drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[40], resolution_information.Heiht + imguiswitch_information.floatswitch[41]}, 5, ImColor(255, 255, 255, 255));
						}
						if (CapTivity(imguiswitch_information.floatswitch[43], Shared_Information[OBJECTCOUNT[0]].RadarCoordinates))
						{

							drawlist->AddCircleFilled({resolution_information.Width + imguiswitch_information.floatswitch[40] + Shared_Information[OBJECTCOUNT[0]].RadarCoordinates.x, resolution_information.Heiht + imguiswitch_information.floatswitch[41] + Shared_Information[OBJECTCOUNT[0]].RadarCoordinates.y}, 5, Shared_Information[OBJECTCOUNT[0]].ColorChanGeable);
						}
					}
					// 这以内是人物信息
					if (Shared_Information[OBJECTCOUNT[0]].IsBoot == 1)
					{
						人机数量++;
					}
					else
					{
						真人数量++;
					}
					OBJECTCOUNT[0]++;
				}
			}
			if (initializeaimi)
			{
				if (!aimiswitch)
				{
					aimi_abs = 0;
					aimitar = -1;
					for (int i = 0; i < OBJECTCOUNT[0]; i++)
					{
						aimifal = (imguiswitch_information.intswitch[52] == 1 && Shared_Information[i].Health <= 0);

						aimibot = (imguiswitch_information.intswitch[54] == 1 && Shared_Information[i].IsBoot == 1);

						aimi_x_abs = abs(resolution_information.Width - Shared_Information[i].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].x);
						aimi_y_abs = abs(resolution_information.Heiht - Shared_Information[i].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].y);
						if (Shared_Information[i].ScreenCamera > 0.01 && (Shared_Information[i].Distance <= imguiswitch_information.floatswitch[51] || Shared_Information[i].Distance <= imguiswitch_information.floatswitch[56]) && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && aimi_x_abs < imguiswitch_information.floatswitch[50] && aimi_y_abs < imguiswitch_information.floatswitch[50])
						{
							switch (imguiswitch_information.intswitch[53])
							{
							case 0:
							{
								if (aimi_abs == 0)
								{
									aimitar = i;
									aiminame = Shared_Information[i].PlayerName;
									aimi_abs = aimi_x_abs + aimi_y_abs;
								}
								else
								{
									if (aimi_abs > aimi_x_abs + aimi_y_abs)
									{
										aimitar = i;
										aiminame = Shared_Information[i].PlayerName;
										aimi_abs = aimi_x_abs + aimi_y_abs;
									}
								}
							}
							break;
							case 1:
							{
								if (aimi_abs == 0)
								{
									aimitar = i;
									aiminame = Shared_Information[i].PlayerName;
									aimidistance = Shared_Information[i].Distance;
									aimi_abs = aimi_x_abs + aimi_y_abs;
								}
								else
								{
									if (aimidistance > Shared_Information[i].Distance)
									{
										aimitar = i;
										aiminame = Shared_Information[i].PlayerName;
										aimidistance = Shared_Information[i].Distance;
										aimi_abs = aimi_x_abs + aimi_y_abs;
									}
								}
							}
							break;
							}
						}
					}
					if (aimitar == -1)
					{
						aimiswitch = false;
					}
					else if (aimitar > -1)
					{
						aimiswitch = true;
						aimitiem.looptimesta();
						aimista = Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]];
						aimiend = Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]];
					}
					aiminur = aimitar;
				}
				aimifal = (imguiswitch_information.intswitch[52] == 1 && Shared_Information[aimitar].Health <= 0);
				aimibot = (imguiswitch_information.intswitch[54] == 1 && Shared_Information[aimitar].IsBoot == 1);

				aimi_x_abs = abs(resolution_information.Width - Shared_Information[aimitar].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].x);
				aimi_y_abs = abs(resolution_information.Heiht - Shared_Information[aimitar].ScreenSkeletonCoordinates[imguiswitch_information.intswitch[51]].y);
				if (Shared_Information[aimitar].ScreenCamera > 0.01 && Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[51] && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch && ((IsFire == 1 || IsFire == 257) || (IsCamera == 1 || IsCamera == 257) || 识别喷子(自身武器)) && aimi_x_abs < imguiswitch_information.floatswitch[50] && aimi_y_abs < imguiswitch_information.floatswitch[50])
				{
					if (aiminame != Shared_Information[aimitar].PlayerName)
					{
						for (int i = 0; i < OBJECTCOUNT[0]; i++)
						{
							if (aiminame == Shared_Information[i].PlayerName)
							{
								aimitar = i;
								break;
							}
						}
					}
					looptime = aimitiem.getlooptime();
					if (looptime >= 100000000)
					{
						if (aimista != Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]])
						{
							float distance = GetDistance(aimista, Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]]);
							float speed = (distance / (looptime * 0.00000001));
							aimiant = (Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]] - aimista) / distance * ((Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[52]) * (speed * imguiswitch_information.floatswitch[52]));
							aimiend = (aimiant + Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]]);
						}
						else
						{
							aimiend = Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]];
						}
						aimitiem.looptimesta();
						aimista = Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]];
					}
					else
					{
						aimiend = aimiant + Shared_Information[aimitar].WorldSkeletonCoordinates[imguiswitch_information.intswitch[51]];
					}
					if ((IsFire == 1 || IsFire == 257) || (IsCamera == 1 || IsCamera == 257) || 识别喷子(自身武器))
					{

						if (自身动作 == 272 || 自身动作 == 1296 || 自身动作 == 1297 || 自身动作 == 273 || 自身动作 == 4368 || 自身动作 == 4369 || 自身动作 == 5392 || 自身动作 == 5393 || 自身动作 == 400 || 自身动作 == 403)
						{
							if (Shared_Information[aimitar].Distance > 200)
							{
								SelfViewCoordinate.z += Shared_Information[aimitar].Distance * ycsz * WeaponPressureGunValue(SelfHandheldWeaponValue);
							}
							else
							{
								SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[53] * WeaponPressureGunValue(SelfHandheldWeaponValue);
							}
						}
						else if (/*SelfAction == 257*/ 自身动作 == 288 || 自身动作 == 289 || 自身动作 == 4384 || 自身动作 == 4385 || 自身动作 == 5408 || 自身动作 == 5409 || 自身动作 == 1312 || 自身动作 == 1313)
						{
							if (Shared_Information[aimitar].Distance > 200)
							{
								SelfViewCoordinate.z += Shared_Information[aimitar].Distance * ycsd * WeaponPressureGunValue(SelfHandheldWeaponValue);
							}
							else
							{
								SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[54] * WeaponPressureGunValue(SelfHandheldWeaponValue);
							}
						}
						else if (/*SelfAction == 258*/ 自身动作 == 320 || 自身动作 == 1344)
						{
							if (Shared_Information[aimitar].Distance > 200)
							{
								SelfViewCoordinate.z += Shared_Information[aimitar].Distance * ycsp * WeaponPressureGunValue(SelfHandheldWeaponValue);
							}
							else
							{
								SelfViewCoordinate.z += Shared_Information[aimitar].Distance * imguiswitch_information.floatswitch[55] * WeaponPressureGunValue(SelfHandheldWeaponValue);
							}
						}

						touchdriven.setFireIo(true);
					}
					else
					{
						touchdriven.setFireIo(false);
					}
					if (aimiend.x != 0 || aimiend.y != 0 || aimiend.z != 0)
					{
						touch_information.AimingCoordinates = FastAtan2(aimiend, SelfViewCoordinate);
					}
					// cout<<touch_information.AimingCoordinates.x<<"   "<<touch_information.AimingCoordinates.y<<endl;
					aimifal = (imguiswitch_information.intswitch[52] == 1 && Shared_Information[aimitar].Health <= 0);
					aimibot = (imguiswitch_information.intswitch[54] == 1 && Shared_Information[aimitar].IsBoot == 1);

					if (!识别喷子(自身武器))
					{
						switch (imguiswitch_information.intswitch[50])
						{
						case 0:
						{
							if (Shared_Information[aimitar].ScreenCamera > 0.01 && Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[51] && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch && (IsFire == 1 || IsFire == 256))
							{
								if (aiminame != Shared_Information[aimitar].PlayerName)
								{
									for (int i = 0; i < OBJECTCOUNT[0]; i++)
									{
										if (aiminame == Shared_Information[i].PlayerName)
										{
											aimitar = i;
											break;
										}
									}
								}

								touchdriven.setAimIo(true);
							}
							else
							{
								aimiswitch = false;
								touchdriven.setAimIo(false);
								memset(&aimiant, 0, sizeof(aimiant));
							}
						}
						break;
						case 1:
						{
							if (Shared_Information[aimitar].ScreenCamera > 0.01 && Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[51] && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch && (IsCamera == 257 || IsCamera == 1))
							{
								if (aiminame != Shared_Information[aimitar].PlayerName)
								{
									for (int i = 0; i < OBJECTCOUNT[0]; i++)
									{
										if (aiminame == Shared_Information[i].PlayerName)
										{
											aimitar = i;
											break;
										}
									}
								}

								touchdriven.setAimIo(true);
							}
							else
							{
								aimiswitch = false;
								touchdriven.setAimIo(false);
								memset(&aimiant, 0, sizeof(aimiant));
							}
						}
						break;
						case 2:
						{
							if (Shared_Information[aimitar].ScreenCamera > 0.01 && Shared_Information[aimitar].Distance <= imguiswitch_information.floatswitch[51] && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch && (IsFire == 1 || IsFire == 257) && (IsCamera == 1 || IsCamera == 257))
							{
								if (aiminame != Shared_Information[aimitar].PlayerName)
								{
									for (int i = 0; i < OBJECTCOUNT[0]; i++)
									{
										if (aiminame == Shared_Information[i].PlayerName)
										{
											aimitar = i;
											break;
										}
									}
								}

								touchdriven.setAimIo(true);
							}
							else
							{
								aimiswitch = false;
								touchdriven.setAimIo(false);
								memset(&aimiant, 0, sizeof(aimiant));
							}
						}
						break;
						}
					}
					else
					{
						if (Shared_Information[aimitar].ScreenCamera > 0.01 && Shared_Information[aimitar].Distance <= 20 && imguiswitch_information.intswitch[59] && !aimifal && !aimibot && !touch_information.TouchAimAtControl && aimiswitch && 识别喷子(自身武器) && Shared_Information[aimitar].IsBoot == 0 && Shared_Information[aimitar].Health > 0 && aimi_x_abs < imguiswitch_information.floatswitch[50] && aimi_y_abs < imguiswitch_information.floatswitch[50])
						{
							if (aiminame != Shared_Information[aimitar].PlayerName)
							{
								for (int i = 0; i < OBJECTCOUNT[0]; i++)
								{
									if (aiminame == Shared_Information[i].PlayerName)
									{
										aimitar = i;
										break;
									}
								}
							}

							touchdriven.setAimIo(true);
						}
						else
						{
							aimiswitch = false;
							touchdriven.setAimIo(false);
							memset(&aimiant, 0, sizeof(aimiant));
						}
					}
				}
				else
				{
					aimiswitch = false;
					touchdriven.setAimIo(false);
					memset(&aimiant, 0, sizeof(aimiant));
				}
			}
			float CoordinatesW = resolution_information.ScreenWidth * touch_information.TouchRadius;
			float CoordinatesX = resolution_information.ScreenWidth * touch_information.TouchPoints.y;
			float CoordinatesY = resolution_information.ScreenHeiht * (1 - touch_information.TouchPoints.x);
			if (initializeaimi && imguiswitch_information.boolswitch[50])
			{
				string Text = "";
				Text += "触摸控制区域";
				ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 30);
				drawlist->AddText(NULL, 30, {CoordinatesX - (TextSize.x / 2), CoordinatesY - (TextSize.y / 2)}, ImColor(255, 255, 255, 255), Text.c_str());
				drawlist->AddRectFilled({CoordinatesX - CoordinatesW, CoordinatesY - CoordinatesW}, {CoordinatesX + CoordinatesW, CoordinatesY + CoordinatesW}, ImColor(200, 0, 0, 100));
			}
			if (imguiswitch_information.boolswitch[51] && initializeaimi && dynamic)
			{ // 自瞄圈
				float AimDs;
				if (aimitar == -1)
				{
					AimDs = imguiswitch_information.floatswitch[50];
				}
				else
				{
					AimDs = sqrt(pow(resolution_information.Width - Shared_Information[aimitar].ScreenCoordinates.x, 2) + pow(resolution_information.Heiht - Shared_Information[aimitar].HeadSkeletonCoordinates, 2));
				}
				drawlist->AddCircle({resolution_information.Width, resolution_information.Heiht}, AimDs, ImColor(255, 255, 255, 255), 0, 2);
			}
			else
			{
				drawlist->AddCircle({resolution_information.Width, resolution_information.Heiht}, imguiswitch_information.floatswitch[50], ImColor(255, 255, 255, 255), 0, 2);
			}
			string Text = "";

			// 灵动岛
			drawDynamicIsland(drawlist, aimitar, resolution_information, Shared_Information, 真人数量, 人机数量);
		}
		else if (!IsGameStart)
		{
			string Text = "";
			Text += "等待进入游戏对局";
			ImVec2 TextSize = ImGui::CountTextSize(NULL, Text.c_str(), 35);
			drawlist->AddText(NULL, 35, {resolution_information.Width - (TextSize.x / 2), 260 - (TextSize.y / 2)}, ImColor(200, 50, 50, 255), Text.c_str());
		}
	}
}

float ImGuiELGS::WeaponPressureGunValue(int Index)
{
	switch (Index)
	{
	case 101008:
		return 0.95; // M762
		break;
	case 101001:
		return 0.95; // AKM
		break;
	case 101005:
		return 0.95; // Groza
		break;
	case 101012:
		return 0.95; // 蜜獾突击步枪
		break;
	case 101004:
		return 0.85; // M416
		break;
	case 101003:
		return 0.85; // SCAR-L
		break;
	case 101002:
		return 0.85; // M16A4
		break;
	case 101009:
		return 0.85; // Mk47
		break;
	case 101006:
		return 0.85; // AUG
		break;
	case 101010:
		return 0.85; // G36C
		break;
	case 101007:
		return 0.85; // QBZ
		break;
	case 101011:
		return 0.85; // AC-VAL
		break;
	case 105001:
		return 0.62; // M249
		break;
	case 105002:
		return 0.62; // DP-28
		break;
	case 105010:
		return 0.62; // MG3
		break;
	case 102001:
		return 0.55; // UZI
		break;
	case 102003:
		return 0.55; // Vector
		break;
	case 100103:
		return 0.55; // PP-19
		break;
	case 102007:
		return 0.55; // MP5K
		break;
	case 102002:
		return 0.55; // UMP 5
		break;
	case 102004:
		return 0.55; // 汤姆逊
		break;
	case 102105:
		return 0.55; // P90
		break;
	case 102005:
		return 0.55; // 野牛
		break;
	default:
		return 0.75; // 未收录
		break;
	}
	return 0;
}
