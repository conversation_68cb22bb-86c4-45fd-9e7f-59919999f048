#ifndef KKONDK_TOUCH_H
#define KKONDK_TOUCH_H
#include <linux/input.h>
#include <linux/types.h>
#include <fcntl.h>
#include <sys/stat.h>
#include <dirent.h>
#include <linux/uinput.h>
#include <iostream>
#include <cstdio>
#include <cstdlib>
#include <string>
#include <regex>
#include <sstream>
#include <thread>
#include <chrono>
#include "VecTool.h"
#include "../src/ImGuiTOOL/driver.h"

VecTor2 FastAtan2(VecTor3 Enem, VecTor3 Self) {
	VecTor2 AimCoordinates;
    float RotationX = Enem.x - Self.x;
    float RotationY = Enem.y - Self.y;
    float RotationZ = Enem.z - Self.z;
    float RotationH = sqrt((RotationX * RotationX) + (RotationY * RotationY));
	AimCoordinates.x = atan2(RotationZ, RotationH) * 180 / M_PI;
	if (RotationX >= 1 && RotationY >= 1) {
    	AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI;
	} else if (RotationX >= 1 && RotationY <= 1) {
    	AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI;
    } else if (RotationX <= 1 && RotationY >= 1) {
    	AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI + 180;
    } else if (RotationX <= 1 && RotationY <= 1) {
    	AimCoordinates.y = atan(RotationY / RotationX) * 180 / M_PI - 180;
    }
    return AimCoordinates;
}

#define test_bit(array, bit) ((array[bit / BITS_PER_LONG] >> bit % BITS_PER_LONG) & 1)
#define NBITS(x) ((((x) - 1) / BITS_PER_LONG) + 1)

//触控命令存储数据结构
struct Touch {
	int TRACKING_ID;
	int POSITION_X;
	int POSITION_Y;
	int SLOT;
	bool io;
	bool Action;
	bool run;
	bool isbot;
	bool runing;
};

//触控设备数据结构
struct eventinfo {
	int ID;
	bool io = false;
	struct input_absinfo data;
};

//触控设备数据结构
struct events {
	int ID;
	bool io = false;
	bool infoio = false;
	eventinfo eventmsg[KEY_MAX + 1];
} eventdata[EV_MAX + 1];

class touch {
    private:
	bool mainio;
	int write_fb;
	bool Aimio = false;
	bool IsFire = false;
    Timer MainAimiSleep;
    int FullScreenX = 0;
	int FullScreenY = 0;
	bool AimAtIo = false;
	VecTor2 TouchProportion;
	TOUCH_INFORMATION* touch_information = nullptr;
	RESOLUTION_INFORMATION* resolution_information = nullptr;
	
	VecTor2 RotatePoint(float PointX, float PointY, VecTor2 Display) {
        VecTor2 Rotate = {0, 0};
        if (resolution_information->Orientation == 3) {
            Rotate.x = Display.y - PointY;
            Rotate.y = PointX;
        } else if (resolution_information->Orientation == 2) {
            Rotate.x = Display.x - PointX;
            Rotate.y = Display.y - PointY;
        } else if (resolution_information->Orientation == 1) {
            Rotate.x = PointY;
            Rotate.y = Display.x - PointX;
        } else if (resolution_information->Orientation == 0) {
    		Rotate.x = PointX;
        	Rotate.y = PointY;
		}
    	return Rotate;
    }
	
    struct BotTouch {
        VecTor2 endLU;
        VecTor2 endRD;
    	VecTor2 mspeed;
        VecTor2 maxMove;
    	VecTor2 AimMove;
        VecTor2 Touchsize;
    	VecTor2 nowPointer;
        long long loadTime;
        bool botio = false;
    	VecTor2 beginPointer;
        bool botstart = false;
    	float ThreadSpeed = 0;
        VecTor2 AimRot = {0, 0};
    	VecTor2 *TouchProportion;
    	TOUCH_INFORMATION* touch_information = nullptr;
        struct timespec strtime, endtime, Intervaltime;
    	RESOLUTION_INFORMATION* resolution_information = nullptr;
    	
        BotTouch(TOUCH_INFORMATION* _touch_information, RESOLUTION_INFORMATION* _resolution_information, VecTor2 Touchsize_) {
    		if (_touch_information != nullptr && _resolution_information != nullptr) {
    			touch_information = _touch_information;
    			resolution_information = _resolution_information;
    		}
    		clock_gettime(CLOCK_MONOTONIC, &strtime);
            Touchsize = Touchsize_;
            beginPointer.x = Touchsize.x * touch_information->TouchPoints.x;
            beginPointer.y = Touchsize.y * touch_information->TouchPoints.y;
            endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
            endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
            endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
            endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
            maxMove.x = (Touchsize.x * (touch_information->TouchRadius) / (touch_information->floatswitch[0] * 0.2));
            maxMove.y = (Touchsize.y * (touch_information->TouchRadius) / (touch_information->floatswitch[0] * 0.2));
            ThreadSpeed = 2000 / touch_information->floatswitch[0];
            botio = false;
        }
    	
        void setTouch() {
            if (resolution_information->Orientation == 1) {
                beginPointer.x = Touchsize.x * touch_information->TouchPoints.x;
                beginPointer.y = Touchsize.y * touch_information->TouchPoints.y;
                endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
                endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
                endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
                endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
                maxMove.x = (Touchsize.x * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                maxMove.y = (Touchsize.y * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                ThreadSpeed = 2000 / touch_information->floatswitch[0];
            } else if (resolution_information->Orientation == 3) {
                beginPointer.x = Touchsize.x * (1 - touch_information->TouchPoints.x);
                beginPointer.y = Touchsize.y * (1 - touch_information->TouchPoints.y);
                endLU.x = beginPointer.x - Touchsize.x * (touch_information->TouchRadius);
                endLU.y = beginPointer.y - Touchsize.y * (touch_information->TouchRadius);
                endRD.x = beginPointer.x + Touchsize.x * (touch_information->TouchRadius);
                endRD.y = beginPointer.y + Touchsize.y * (touch_information->TouchRadius);
                maxMove.x = (Touchsize.x * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                maxMove.y = (Touchsize.y * touch_information->floatswitch[3] / touch_information->floatswitch[0]);
                ThreadSpeed = 2000 / touch_information->floatswitch[0];
            }
        }
    	
        bool StartAim() {
            if (botio) {
                clock_gettime(CLOCK_MONOTONIC, &endtime);
                loadTime = ((1000000000 * endtime.tv_sec) + (endtime.tv_nsec)) - ((1000000000 * strtime.tv_sec) + (strtime.tv_nsec));
                if (loadTime >= 5000000) {
                    // 先进行延迟 防止速度太快导致跳跃
                    std::this_thread::sleep_for(std::chrono::milliseconds(10));
                    nowPointer = beginPointer;
                    return true;
                }
                return false;
            } else {
                // 先进行延迟 防止速度太快导致跳跃
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
                nowPointer = beginPointer;
                botio = true;
                botstart = true;
                mspeed.x = 0.2;
                mspeed.y = 0.2;
                return true;
            }
        }
    	
        float GetMinAngle(float Target, float self, float offset) {
            float d1, d2;
            Target += offset;
            d1 = Target - self;
            d2 = 360.0 - fabs(d1);
            if (d1 > 0) {
                d2 *= -1.0;
            }
            return fabs(d1) < fabs(d2) ? d1 : d2;
        }
    	
        void setTouchProportion(VecTor2 *TouchProportion_) {
            TouchProportion = TouchProportion_;
        }
    	
        bool AimRotArithmetic(VecTor2 TouchMouses, VecTor2 TouchAimRot) {
            if (isnormal(TouchAimRot.x) && isnormal(TouchAimRot.y)) {
                if (resolution_information->Orientation == 1) {
                    AimRot.x = GetMinAngle(TouchAimRot.x, TouchMouses.x, 0.05);
                    AimRot.y = GetMinAngle(TouchAimRot.y, TouchMouses.y, 0.03);
                } else if (resolution_information->Orientation == 3) {
                    AimRot.x = -GetMinAngle(TouchAimRot.x, TouchMouses.x, 0.05);
                    AimRot.y = -GetMinAngle(TouchAimRot.y, TouchMouses.y, 0.03);
                }
                if (fabs(AimRot.x) > (0.25 / touch_information->Scal)) {
                    mspeed.x += 0.1;
                    if (mspeed.x > (touch_information->floatswitch[2] * ThreadSpeed) * touch_information->Scal) {
                        mspeed.x = (touch_information->floatswitch[2] * ThreadSpeed);
                    }
                } else {
                    mspeed.x -= 0.1;
                    if (mspeed.x < 0.2) {
                        mspeed.x = 0.2;
                    }
                }
                if (fabs(AimRot.y) > (0.25 / touch_information->Scal)) {
                    mspeed.y += 0.1;
                    if (mspeed.y > (touch_information->floatswitch[1] * ThreadSpeed) * touch_information->Scal) {
                        mspeed.y = (touch_information->floatswitch[1] * ThreadSpeed);
                    }
                } else {
                    mspeed.y -= 0.1;
                    if (mspeed.y < 0.2) {
                        mspeed.y = 0.2;
                    }
                }
                if (fabs(AimRot.x) > maxMove.x) {
                    AimMove.x = AimRot.x > 0 ? maxMove.x : -(maxMove.x);
                    nowPointer.x += AimMove.x;
                } else {
                    AimMove.x = (AimRot.x * mspeed.x) * TouchProportion->x;
                    nowPointer.x += AimMove.x;
                }
                if (fabs(AimRot.y) > maxMove.y) {
                    AimMove.y = AimRot.y > 0 ? maxMove.y : -(maxMove.y);
                    nowPointer.y += AimMove.y;
                } else {
                    AimMove.y = (AimRot.y * mspeed.y) * TouchProportion->y;
                    nowPointer.y += AimMove.y;
                }
                if (nowPointer.x < endLU.x || nowPointer.y < endLU.y || nowPointer.x > endRD.x || nowPointer.y > endRD.y) {
                    clock_gettime(CLOCK_MONOTONIC, &strtime);
                    return false;
                }
                return true;
            } else {
                if (nowPointer.x < endLU.x || nowPointer.y < endLU.y || nowPointer.x > endRD.x || nowPointer.y > endRD.y) {
                    clock_gettime(CLOCK_MONOTONIC, &strtime);
                    return false;
                }
                return true;
            }
        }
    	
        void botEnd() {
            botio = false;
        }
    };
	
    struct touchInput {
    	int pos;
    	int lastid;
    	Touch bottouch;
    	size_t outsize;
    	Touch touch[16];
    	Touch *outTouch[16];
    	VecTor2 *TouchProportion;
        Touch *touchNow = nullptr;
        Touch *touchLast = nullptr;
        struct input_event oneevent;
    	TOUCH_INFORMATION* touch_information = nullptr;
    	RESOLUTION_INFORMATION* resolution_information = nullptr;
    	
        void setTouchProportion(VecTor2 *TouchProportion_) {
            TouchProportion = TouchProportion_;
        }
    	
        touchInput(TOUCH_INFORMATION* _touch_information, RESOLUTION_INFORMATION* _resolution_information) {
    		if (_touch_information != nullptr && _resolution_information != nullptr) {
    			touch_information = _touch_information;
    			resolution_information = _resolution_information;
    		}
            bzero(touch, sizeof(touch));
            if (touchNow == nullptr) {
                touchNow = &touch[0];
            }
            pos = 0;
            for (int l = 0; l < 16; ++l) {
                touch[l].SLOT = l;
            }
            SLOTio = false;
            outsize = 0;
        }
    	
        Touch *opennew(int s) {
    		int in = 0;
            for (in = 0; in < 16; ++in) {
                if (!touch[in].io) {
                    break;
                }
            }
            touch[in].io = true;
            touch[in].Action = true;
            touch[in].run = true;
            touchLast = &touch[in];
            touchLast->TRACKING_ID = s;
            ++pos;
            return touchLast;
        }
    
        bool SLOTio = false;
        Touch *SLOT_getnow(int idi, bool bot) {
            if (bot) {
                if (!bottouch.io) {
                    bottouch.Action = true;
                    bottouch.run = true;
                }
                bottouch.isbot = bot;
                bottouch.SLOT = idi;
                bottouch.io = true;
                return touchNow;
            } else {
                SLOTio = true;
                if (!touch[idi].io) {
                    ++pos;
                    touch[idi].Action = true;
                    touch[idi].run = true;
                }
                touch[idi].io = true;
                touch[idi].SLOT = idi;
                touchLast = &touch[idi];
                return touchLast;
            }
        }
    	
        void closenow(Touch *last) {
            if (pos > 0) {
                --pos;
            }
            last->io = false;
            last->TRACKING_ID = -1;
        }
    	
        Touch *TRACKING_ID_findnow(int ss, bool bot) {
            if (bot) {
                if (ss == -1) {
                    bottouch.io = false;
                }
                bottouch.TRACKING_ID = ss;
                return touchNow;
            } else {
                bool a = false;
                if (ss == -1) {
                    closenow(touchNow);
                    SLOTio = false;
                    return touchNow;
                }
                if (SLOTio) {
                    touchLast->TRACKING_ID = ss;
                    SLOTio = false;
                    return touchLast;
                }
    			int in = 0;
                for (in = 0; in < 16; ++in) {
                    if (touch[in].TRACKING_ID == ss) {
                        a = true;
                        break;
                    }
                }
                if (a) {
                    touchLast = &touch[in];
                    SLOTio = false;
                    return touchLast;
                } else {
                    SLOTio = false;
                    return opennew(ss);
                }
            }
        }
    	
        void ABS_SetX(int x, bool bot) {
            if (bot) {
                bottouch.POSITION_X = x;
                bottouch.runing = true;
            } else {
                touchNow->POSITION_X = x;
                touchNow->runing = true;
            }
        }
    	
        void ABS_SetY(int y, bool bot) {
            if (bot) {
                bottouch.POSITION_Y = y;
                bottouch.runing = true;
            } else {
                touchNow->POSITION_Y = y;
                touchNow->runing = true;
            }
        }
		
		VecTor2 RotatePoint(float PointX, float PointY, VecTor2 Display) {
            VecTor2 Rotate = {0, 0};
            if (resolution_information->Orientation == 3) {
                Rotate.x = Display.y - PointY;
                Rotate.y = PointX;
            } else if (resolution_information->Orientation == 2) {
                Rotate.x = Display.x - PointX;
                Rotate.y = Display.y - PointY;
            } else if (resolution_information->Orientation == 1) {
                Rotate.x = PointY;
                Rotate.y = Display.x - PointX;
            } else if (resolution_information->Orientation == 0) {
    			Rotate.x = PointX;
                Rotate.y = PointY;
    		}
            return Rotate;
        }
    	
		int touchnewX = 0;
		int touchnewY = 0;
        int add_event(input_event &oneevent, bool isbot) {
            int SYN = 0;
            switch (oneevent.code) {
                case ABS_MT_POSITION_X:
    				touchnewX = oneevent.value;
                    ABS_SetX(oneevent.value, isbot);
                break;
                case ABS_MT_POSITION_Y:
    				touchnewY = oneevent.value;
                    ABS_SetY(oneevent.value, isbot);
                break;
                case ABS_MT_TRACKING_ID:
                    touchNow = TRACKING_ID_findnow(oneevent.value, isbot);
                    touchNow->isbot = isbot;
    				if ((int)oneevent.value >= 1) {
    					ImGui::GetIO().MouseDown[0] = true;
    				} else {
    					ImGui::GetIO().MouseDown[0] = false;
    				}
                break;
                case ABS_MT_SLOT:
                    touchNow = SLOT_getnow(oneevent.value, isbot);
                    touchNow->isbot = isbot;
                break;
                case SYN_REPORT:
                    SYN = 1;
                break;
            }
    		VecTor2 Point = RotatePoint(touchnewX, touchnewY, touch_information->TouchScreenSize);
    		ImGui::GetIO().MousePos = ImVec2((Point.x * resolution_information->FixedScreenWidth) / touch_information->TouchScreenSize.x, (Point.y * resolution_information->FixedScreenHeiht) / touch_information->TouchScreenSize.y);
            if (SYN) {
                return 1;
            }
            return 0;
        }
    
        int upcon;
    	int tmpsize;
    	Touch tmp[9];
        int thindx[9];
        bool jk = true;
        int tmpcon = 0;
    	Timer movetiem;
        void GetRightTouch() {
            if (jk) {
                tmpcon = 0;
                for (int l = 0; l < outsize; ++l) {
                    if (outTouch[l]->io) {
                        if (resolution_information->Orientation == 1) {
                            if (!outTouch[l]->isbot && outTouch[l]->POSITION_Y / TouchProportion->y > (resolution_information->ScreenWidth / 2)) {
                                tmp[tmpcon] = *outTouch[l];
                                thindx[tmpcon] = l;
                                touch_information->TouchAimAtControl = false;
                                jk = false;
                                ++tmpcon;
                                movetiem.looptimesta();
                            }
                        } else if (resolution_information->Orientation == 3) {
                            if (!outTouch[l]->isbot && outTouch[l]->POSITION_Y / TouchProportion->y < (resolution_information->ScreenWidth / 2)) {
                                tmp[tmpcon] = *outTouch[l];
                                thindx[tmpcon] = l;
                                touch_information->TouchAimAtControl = false;
                                jk = false;
                                ++tmpcon;
                                movetiem.looptimesta();
                            }
                        }
                    }
                }
                tmpsize = tmpcon;
            } else {
                upcon = 0;
                for (int l = 0; l < tmpsize; ++l) {
                    if (outTouch[l]->TRACKING_ID == -1) {
                        ++upcon;
                    } else {
                        if (movetiem.getlooptime() >= 150000000) {
                            if (abs(tmp[l].POSITION_Y - outTouch[thindx[l]]->POSITION_Y) > 100 || abs(tmp[l].POSITION_X - outTouch[thindx[l]]->POSITION_X) > 100) {
                                touch_information->TouchAimAtControl = true;
                                tmp[l].POSITION_Y = outTouch[thindx[l]]->POSITION_Y;
                                tmp[l].POSITION_X = outTouch[thindx[l]]->POSITION_X;
                            } else {
    							touch_information->TouchAimAtControl = false;
                                tmp[l].POSITION_Y = outTouch[thindx[l]]->POSITION_Y;
                                tmp[l].POSITION_X = outTouch[thindx[l]]->POSITION_X;
                                jk = true;
                            }
                            movetiem.looptimesta();
                        }
                    }
                }
                if (upcon == tmpsize) {
                    jk = true;
                    touch_information->TouchAimAtControl = false;
                }
            }
        }
    	
        void getOutEvent() {
            int un = 0;
            for (int l = 0; l < 10; ++l) {
                if (touch[l].run) {
                    outTouch[un] = &touch[l];
                    ++un;
                }
            }
            if (bottouch.run) {
                outTouch[un] = &bottouch;
                ++un;
            }
            outsize = un;
        }
    	
        void readEvent() {
            for (;;) {
                if (read(touch_information->TouchDeviceFile, &oneevent, sizeof(oneevent)) > 0) {
                    if (add_event(oneevent, false)) {
                        break;
                    }
                } else {
                    break;
                }
            }
        }
    };
	
    struct touchUnput {
    	float proportionX;
    	float proportionY;
    	float POSITION_X_Max;
    	float POSITION_Y_Max;
    	bool *mainio = nullptr;
    	int *write_fb = nullptr;
    	touchInput *intouchdata = nullptr;
    	struct events *eventdata = nullptr;
    	struct input_event ID, SLOT, SLOTAI;
    	TOUCH_INFORMATION* touch_information = nullptr;
    	RESOLUTION_INFORMATION* resolution_information = nullptr;
    	struct input_event PRESSURE, TOUCH_MAJOR, WIDTH_MAJOR, FINGER, inx, iny, SYN, BTOUCH;
    	Driver* driver = nullptr;  // 添加对新内核驱动的引用
    	
    	void setMainio(bool *io) {
    		mainio = io;
    	}
    	
    	void setEventData(struct events *eventdata_) {
    		eventdata = eventdata_;
    	}
    	
    	void setInTouchData(touchInput *intouchdata_) {
    		intouchdata = intouchdata_;
    	}
    	
    	void setWritefb(int *fd) {
    		write_fb = fd;
    	}

    	void setDriver(Driver* driver_) {
    		driver = driver_;
    	}
    	
        touchUnput(TOUCH_INFORMATION* _touch_information, RESOLUTION_INFORMATION* _resolution_information) {
    		if (_touch_information != nullptr && _resolution_information != nullptr) {
    			touch_information = _touch_information;
    			resolution_information = _resolution_information;
    		}
        }
    	
    	void Touch_BTOUCH_SLOT(int io) {
    		// 使用新的内核触摸驱动，这个方法不需要特殊处理
    	}

    	void Touch_Action_SLOT(int x, int y, int slot, int trackingid) {
    		// 使用新的内核触摸驱动进行按下操作
    		if (driver != nullptr) {
    			driver->uinput_down(x, y);
    		}
    	}

    	void Touch_Move_SLOT(int x, int y, int slot) {
    		// 使用新的内核触摸驱动进行移动操作
    		if (driver != nullptr) {
    			driver->uinput_move(x, y);
    		}
    	}

    	void Touch_SYN_SLOT() {
    		// 使用新的内核触摸驱动，SYN 事件由驱动自动处理
    	}

    	void Touch_CLOSE_SLOT(int slot) {
    		// 使用新的内核触摸驱动进行抬起操作
    		if (driver != nullptr) {
    			driver->uinput_up();
    		}
    	}
    	
        char *randomString(int length) {
            int flag, i;
            srand((unsigned)time(NULL));
            char *tmpString = (char*)malloc(length * sizeof(char));
            for (i = 0; i < length - 1; i++) {
                flag = rand() % 3;
                switch (flag) {
                case 0:
                    tmpString[i] = 'A' + rand() % 26;
                    break;
                case 1:
                    tmpString[i] = 'a' + rand() % 26;
                    break;
                case 2:
                    tmpString[i] = '0' + rand() % 10;
                    break;
                default:
                    tmpString[i] = 'x';
                    break;
                }
            }
            tmpString[length - 1] = '\0';
            return tmpString;
        }
    	
    	int createTouchScreen(int *fd_,struct events *eventdata) {
    		static int uinp_fd;
    		struct uinput_user_dev uinp;
    		uinp_fd = open("/dev/uinput", O_RDWR);
    		if (uinp_fd == 0) {
    			return 0;
    		}
    		memset(&uinp, 0, sizeof(uinp));
    		strncpy(uinp.name, randomString(rand() % 20), UINPUT_MAX_NAME_SIZE);
    		uinp.id.bustype = ID_BUS;
    		uinp.id.version = rand() % 10;
    		uinp.id.vendor  = rand() % 20;
    		uinp.id.product = rand() % 30;
    		for (int ev = 0; ev < EV_MAX; ++ev) {
    			if (eventdata[ev].io) {
    				ioctl(uinp_fd, UI_SET_EVBIT, eventdata[ev].ID);
    				if (eventdata[ev].ID == 0){
    					continue;
    				}
    				if (eventdata[ev].infoio) {
    					for (int i = 0; i < ABS_MAX; ++i) {
    						if (eventdata[ev].eventmsg[i].io) {
    							ioctl(uinp_fd, UI_SET_ABSBIT, eventdata[ev].eventmsg[i].ID);
    							if (ABS_MT_POSITION_X == eventdata[ev].eventmsg[i].ID){
    								POSITION_X_Max = eventdata[ev].eventmsg[i].data.maximum;
    								proportionX = POSITION_X_Max / (resolution_information->ScreenHeiht < resolution_information->ScreenWidth ? resolution_information->ScreenHeiht : resolution_information->ScreenWidth);
    							}
    							if (ABS_MT_POSITION_Y == eventdata[ev].eventmsg[i].ID){
    								POSITION_Y_Max = eventdata[ev].eventmsg[i].data.maximum;
    								proportionY = POSITION_Y_Max / (resolution_information->ScreenHeiht > resolution_information->ScreenWidth ? resolution_information->ScreenHeiht : resolution_information->ScreenWidth);
    							}
    							uinp.absmin[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.minimum;
    							uinp.absmax[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.maximum;
    							uinp.absfuzz[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.fuzz;
    							uinp.absflat[eventdata[ev].eventmsg[i].ID] = eventdata[ev].eventmsg[i].data.flat;
    						}
    					}
    				} else {
    					for (int i = 0; i < KEY_MAX; ++i) {
    						if (eventdata[ev].eventmsg[i].io) {
    							ioctl(uinp_fd, UI_SET_KEYBIT, eventdata[ev].eventmsg[i].ID);
    						}
    					}
    				}
    			}
    		}
    		ioctl(uinp_fd, UI_SET_PROPBIT, INPUT_PROP_DIRECT);
    		write(uinp_fd, &uinp, sizeof(uinp));
    		ioctl(uinp_fd, UI_DEV_CREATE);
    		*fd_ = uinp_fd;
    		while (true) {
    			if (!mainio) {
    				break;
    			}
    			usleep(1000000);
    		}
    		return 1;
    	}
    	
    	int unputinit() {
    		PRESSURE.code = ABS_MT_PRESSURE;
    		PRESSURE.type = EV_ABS;
    		PRESSURE.value = 20;
    		TOUCH_MAJOR.type = EV_ABS;
    		TOUCH_MAJOR.code = ABS_MT_TOUCH_MAJOR;
    		TOUCH_MAJOR.value = 15;
    		WIDTH_MAJOR.type = EV_ABS;
    		WIDTH_MAJOR.code = ABS_MT_WIDTH_MAJOR;
    		WIDTH_MAJOR.value = 10;
    		ID.type = EV_ABS;
    		ID.code = ABS_MT_TRACKING_ID;
    		SLOT.type = EV_ABS;
    		SLOT.code = ABS_MT_SLOT;
    		SLOTAI.type = EV_ABS;
    		SLOTAI.code = ABS_MT_SLOT;
    		FINGER.type = EV_KEY;
    		FINGER.code = BTN_TOOL_FINGER;
    		inx.type = EV_ABS;
    		inx.code = ABS_MT_POSITION_X;
    		iny.type = EV_ABS;
    		iny.code = ABS_MT_POSITION_Y;
    		SYN.type = EV_SYN;
    		SYN.code = SYN_REPORT;
    		SYN.value = 0;
    		BTOUCH.type = EV_KEY;
    		BTOUCH.code = BTN_TOUCH;
    		thread Thread_UnPut([this] {
    			createTouchScreen(write_fb, eventdata);
    		});
    		Thread_UnPut.detach();
    		int counts = 0;
    		while (true) {
    			counts++;
    			if (*write_fb > 0){
    				return 1;
    			}
    			if (counts > 10 && *write_fb < 0){
    				return 0;
    			}
    			usleep(100000);
    		}
    	}
    	
    	bool start = false;
    	void writeEvent() {
    		if (intouchdata->outsize){
    			if (!start){
    				Touch_BTOUCH_SLOT(1);
    				start = true;
    			}
    			for (int i = 0; i < intouchdata->outsize; ++i) {
    				if (intouchdata->outTouch[i]->TRACKING_ID > 0){
    					if (intouchdata->outTouch[i]->Action){
    						Touch_Action_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT, intouchdata->outTouch[i]->TRACKING_ID);
    						intouchdata->outTouch[i]->Action = false;
    						intouchdata->outTouch[i]->runing = false;
    					} else {
    						if (intouchdata->outTouch[i]->runing){
    							Touch_Move_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT);
    							intouchdata->outTouch[i]->runing = false;
    						}
    					}
    				} else {
    					Touch_CLOSE_SLOT(intouchdata->outTouch[i]->SLOT);
    					Touch_SYN_SLOT();
    					intouchdata->outTouch[i]->run = false;
    				}
    			}
    			Touch_SYN_SLOT();
    		} else {
    			if (start){
    				for (int i = 0; i < intouchdata->outsize; ++i) {
    					if (intouchdata->outTouch[i]->TRACKING_ID > 0){
    						if (intouchdata->outTouch[i]->Action){
    							Touch_Action_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT, intouchdata->outTouch[i]->TRACKING_ID);
    							intouchdata->outTouch[i]->Action = false;
    						} else{
    							if (intouchdata->outTouch[i]->runing){
    								Touch_Move_SLOT(intouchdata->outTouch[i]->POSITION_X, intouchdata->outTouch[i]->POSITION_Y, intouchdata->outTouch[i]->SLOT);
    								intouchdata->outTouch[i]->runing = false;
    							}
    						}
    					} else{
    						Touch_CLOSE_SLOT(intouchdata->outTouch[i]->SLOT);
    						Touch_SYN_SLOT();
    						intouchdata->outTouch[i]->run = false;
    					}
    				}
    				Touch_BTOUCH_SLOT(0);
    				Touch_SYN_SLOT();
    				start = false;
    			}
    		}
    	}
    };
    
    private:
    Driver* driver = nullptr;  // 内核触摸驱动实例
    int width = 0;             // 屏幕宽度
    int height = 0;            // 屏幕高度

    public:
    // 析构函数，清理资源
    ~touch() {
        if (driver != nullptr) {
            delete driver;
            driver = nullptr;
        }
    }

    // 新的触摸接口函数
    void Touch_Init() {
        if (driver != nullptr) {
            delete driver;  // 清理之前的实例
        }
        driver = new Driver(); // 主进程
        driver->uinput_init(width, height);
    }

    void Touch_Down(int id, int x, int y) {
        // 立即更新 ImGui 鼠标状态，确保响应速度
        ImGui::GetIO().MouseDown[0] = true;
        if (resolution_information != nullptr) {
            VecTor2 Point = RotatePoint(x, y, VecTor2(width, height));
            ImGui::GetIO().MousePos = ImVec2(
                (Point.x * resolution_information->FixedScreenWidth) / width,
                (Point.y * resolution_information->FixedScreenHeiht) / height
            );
        }

        // 然后处理内核触摸驱动
        if (driver != nullptr) {
            driver->uinput_down(x, y);
        }
    }

    void Touch_Move(int id, int x, int y) {
        // 立即更新鼠标位置
        if (resolution_information != nullptr) {
            VecTor2 Point = RotatePoint(x, y, VecTor2(width, height));
            ImGui::GetIO().MousePos = ImVec2(
                (Point.x * resolution_information->FixedScreenWidth) / width,
                (Point.y * resolution_information->FixedScreenHeiht) / height
            );
        }

        // 然后处理内核触摸驱动
        if (driver != nullptr) {
            driver->uinput_move(x, y);
        }
    }

    void Touch_Up(int id) {
        // 立即更新 ImGui 鼠标状态
        ImGui::GetIO().MouseDown[0] = false;

        // 然后处理内核触摸驱动
        if (driver != nullptr) {
            driver->uinput_up();
        }
    }

    // 设置屏幕分辨率
    void SetScreenResolution(int w, int h) {
        width = w;
        height = h;
    }

    // 设置触摸和分辨率信息（用于新的触摸接口）
    void SetTouchInfo(TOUCH_INFORMATION* _touch_information, RESOLUTION_INFORMATION* _resolution_information) {
        touch_information = _touch_information;
        resolution_information = _resolution_information;
    }

    static int print_possible_events(int fd, struct events *outdata) {
		int ress = 0;
        int res, res2;
		const char *label;
        uint8_t *bits = NULL;
        ssize_t bits_size = 0;
        memset(outdata, 0, sizeof(events) * EV_MAX + 1);
        for (int i = 0; i <= EV_MAX; i++) {
            int count = 0;
            while (true) {
                res = ioctl(fd, EVIOCGBIT(i, bits_size), bits);
                if (res < bits_size) {
                    break;
                }
                bits_size = res + 16;
                bits = (uint8_t *) realloc(bits, bits_size * 2);
                if (bits == NULL) {
                    return ress;
                }
            }
            res2 = 0;
            switch (i) {
                case EV_SYN: label = "SYN";
                    break;
                case EV_KEY: res2 = ioctl(fd, EVIOCGKEY(res), bits + bits_size);
                    label         = "KEY";
                    break;
                case EV_REL: label = "REL";
                    break;
                case EV_ABS: label = "ABS";
                    break;
                case EV_MSC: label = "MSC";
                    break;
                case EV_LED: res2 = ioctl(fd, EVIOCGLED(res), bits + bits_size);
                    label         = "LED";
                    break;
                case EV_SND: res2 = ioctl(fd, EVIOCGSND(res), bits + bits_size);
                    label         = "SND";
                    break;
                case EV_SW: res2 = ioctl(fd, EVIOCGSW(bits_size), bits + bits_size);
                    label        = "SW ";
                    break;
                case EV_REP: label = "REP";
                    break;
                case EV_FF: label = "FF ";
                    break;
                case EV_PWR: label = "PWR";
                    break;
                default: res2 = 0;
                    label     = "???";
            }
            for (int j = 0; j < res; j++) {
                for (int k = 0; k < 8; k++) {
                    if (bits[j] & 1 << k) {
                        char down;
                        if (j < res2 && (bits[j + bits_size] & 1 << k)) {
                            down = '*';
                        } else {
                            down = ' ';
                        }
                        if (count == 0) {
                            outdata[i].ID = i;
                            outdata[i].io = true;
                            outdata[i].eventmsg[j * 8 + k].ID = j * 8 + k;
                            outdata[i].eventmsg[j * 8 + k].io = true;
                        } else if ((count & 0x7) == 0 || i == EV_ABS) {
                            outdata[i].ID = i;
                        }
                        outdata[i].io = true;
                        outdata[i].eventmsg[j * 8 + k].ID = j * 8 + k;
                        outdata[i].eventmsg[j * 8 + k].io = true;
                        if (i == EV_ABS) {
                            outdata[i].ID = i;
                            outdata[i].io = true;
                            outdata[i].eventmsg[j * 8 + k].ID = j * 8 + k;
                            outdata[i].eventmsg[j * 8 + k].io = true;
                            struct input_absinfo abs;
                            if (ioctl(fd, EVIOCGABS(j * 8 + k), &abs) == 0) {
                                ++ress;
                                outdata[i].infoio                   = true;
                                outdata[i].eventmsg[j * 8 + k].data = abs;
                            }
                        }
                        count++;
                    }
				}
            }
        }
        if (bits != nullptr){
            free(bits);
        }
        return ress;
    }
	
    int initInput() {
        if (touch_information->TouchDeviceFile < 0) {
            return touch_information->TouchDeviceFile;
        }
        botID.type = EV_ABS;
        botID.code = ABS_MT_TRACKING_ID;
        botSLOT.type = EV_ABS;
        botSLOT.code = ABS_MT_SLOT;
        botX.type = EV_ABS;
        botX.code = ABS_MT_POSITION_X;
        botY.type = EV_ABS;
        botY.code = ABS_MT_POSITION_Y;
        botSYN.type  = EV_SYN;
        botSYN.code  = SYN_REPORT;
        botSYN.value = 0;
        return touch_information->TouchDeviceFile;
    }
	
    void setAimIo(bool Aimio_) {
        Aimio = Aimio_;
    }
	
    struct input_event botSYN, botSLOT, botID, botX, botY;
    void Bot_Touch_Action_SLOT(touchInput &input, int SLOT, int ID, VecTor2 &TouchPointer) {
        // 使用新的内核触摸驱动进行按下操作
        if (driver != nullptr) {
            driver->uinput_down((int)TouchPointer.x, (int)TouchPointer.y);
        }
    }
	
    void Bot_Touch_Move_SLOT(touchInput &input, int SLOT, VecTor2 &TouchPointer) {
        // 使用新的内核触摸驱动进行移动操作
        if (driver != nullptr) {
            driver->uinput_move((int)TouchPointer.x, (int)TouchPointer.y);
        }
    }
	
    void Bot_Touch_CLOSE_SLOT(touchInput &input, int SLOT) {
        // 使用新的内核触摸驱动进行抬起操作
        if (driver != nullptr) {
            driver->uinput_up();
        }
    }
	
    void GetTouchProportion(float touchmax_X, float touchmax_Y) {
        FullScreenX = resolution_information->FixedScreenWidth;
        FullScreenY = resolution_information->FixedScreenHeiht;
        TouchProportion.x  = touchmax_X / FullScreenY;
        TouchProportion.y  = touchmax_Y / FullScreenX;
    }
    
    void setFireIo(bool io){
        IsFire = io;
    }
	
    void GetTouch(TOUCH_INFORMATION* _touch_information, RESOLUTION_INFORMATION* _resolution_information, bool isreadtouch) {
		if (_touch_information != nullptr && _resolution_information != nullptr) {
			touch_information = _touch_information;
			resolution_information = _resolution_information;
		}
		int touchnewX = 0;
		int touchnewY = 0;
		if (isreadtouch) {
			while (touch_information->TouchDeviceFile) {
        		struct input_event oneevent;
        		if (read(touch_information->TouchDeviceFile, &oneevent, sizeof(oneevent))) {
					if (oneevent.code == ABS_MT_POSITION_X) {
                		touchnewX = oneevent.value;
            		}
            		if (oneevent.code == ABS_MT_POSITION_Y) {
                		touchnewY = oneevent.value;
            		}
            		if (oneevent.code == ABS_MT_TRACKING_ID) {
                		if ((int)oneevent.value >= 1) {
    						ImGui::GetIO().MouseDown[0] = true;
    					} else {
    						ImGui::GetIO().MouseDown[0] = false;
    					}
            		}
					VecTor2 Point = RotatePoint(touchnewX, touchnewY, touch_information->TouchScreenSize);
    				ImGui::GetIO().MousePos = ImVec2((Point.x * resolution_information->FixedScreenWidth) / touch_information->TouchScreenSize.x, (Point.y * resolution_information->FixedScreenHeiht) / touch_information->TouchScreenSize.y);
				}
				//this_thread::sleep_for(1ms);
			}
		} else if (!isreadtouch) {
            touchInput InPut(_touch_information, _resolution_information);
            touchUnput UnPut(_touch_information, _resolution_information);
            Aimio = false;
            if (initInput() < 0) {
                return;
            }
            print_possible_events(touch_information->TouchDeviceFile, eventdata);
            mainio = true;
            UnPut.setMainio(&mainio);
            UnPut.setWritefb(&write_fb);
            UnPut.setEventData(eventdata);
            UnPut.setInTouchData(&InPut);
            if (!UnPut.unputinit()) {
    			return;
            }
            GetTouchProportion(UnPut.POSITION_X_Max, UnPut.POSITION_Y_Max);
            InPut.setTouchProportion(&TouchProportion);
            BotTouch botTouch(_touch_information, _resolution_information, VecTor2(UnPut.POSITION_X_Max, UnPut.POSITION_Y_Max));
            botTouch.setTouchProportion(&TouchProportion);
            int ioctl_result = ioctl(touch_information->TouchDeviceFile, EVIOCGRAB, 1);
    		if (ioctl_result == -1) {
    			return;
    		}
    		
            MainAimiSleep.SetFps(touch_information->floatswitch[0]);
            MainAimiSleep.AotuFPS_init();
            
            for (;;) {
                InPut.readEvent();
                InPut.getOutEvent();
                if (!IsFire){
                    InPut.GetRightTouch();
                } else {
                    touch_information->TouchAimAtControl = false;
                }
                if (Aimio) {
                    if (touch_information->TouchOrientationControl) {
                        GetTouchProportion(UnPut.POSITION_X_Max, UnPut.POSITION_Y_Max);
                        botTouch.setTouch();
                        touch_information->TouchOrientationControl = false;
                    }
                    if (!AimAtIo) {
                        if (botTouch.StartAim()) {
                            Bot_Touch_Action_SLOT(InPut, 8, 1000, botTouch.beginPointer);
                            AimAtIo = true;
                        }
                    } else {
                        if (botTouch.AimRotArithmetic(touch_information->MouseCoordinate, touch_information->AimingCoordinates)) {
                            Bot_Touch_Move_SLOT(InPut, 8, botTouch.nowPointer);
                        } else {
                            Bot_Touch_CLOSE_SLOT(InPut, 8);
                            AimAtIo = false;
                        }
                    }
                } else {
                    if (AimAtIo) {
                        botTouch.botEnd();
                        Bot_Touch_CLOSE_SLOT(InPut, 8);
                        AimAtIo = false;
                    }
                }
                InPut.getOutEvent();
                UnPut.writeEvent();
                if (!mainio) {
                    return;
                }
                
                MainAimiSleep.AotuFPS();
                MainAimiSleep.SetFps(touch_information->floatswitch[0]);
                
            }
		}
    }
};

#endif
